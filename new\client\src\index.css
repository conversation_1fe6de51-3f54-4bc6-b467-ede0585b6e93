@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-dark-950 text-white font-body;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    min-height: 100vh;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/20 backdrop-blur-md border border-white/10;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent;
  }

  .neon-glow {
    @apply shadow-neon;
    filter: drop-shadow(0 0 10px currentColor);
  }

  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-glass;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-glow;
  }

  .btn-secondary {
    @apply glass text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:bg-white/20;
  }
}